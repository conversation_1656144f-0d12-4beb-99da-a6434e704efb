/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

/* Container */
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-input {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    outline: none;
    width: 300px;
    transition: all 0.3s ease;
}

.search-input:focus {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-btn {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    color: white;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.search-btn:hover {
    transform: scale(1.1);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    gap: 2rem;
    padding: 2rem;
    overflow: hidden;
}

/* Games Section */
.games-section {
    flex: 2;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.games-section::-webkit-scrollbar {
    display: none;
}

.section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #ffffff;
    font-weight: 600;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding-right: 1rem;
}

/* Game Card */
.game-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-card:hover {
    transform: translateY(-5px) scale(1.02);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.game-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover .game-image {
    transform: scale(1.05);
}

.game-info {
    padding: 1rem;
}

.game-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.game-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

/* Leaderboard Section */
.leaderboard-section {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: fit-content;
}

.leaderboard-placeholder {
    text-align: center;
    padding: 2rem 0;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.placeholder-content p {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.placeholder-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Game Overlay */
.game-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: none;
    z-index: 1000;
}

.game-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-container {
    width: 95%;
    height: 95%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-header {
    background: rgba(0, 0, 0, 0.5);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.home-btn, .close-btn {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    color: white;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.home-btn:hover, .close-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.game-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #ffffff;
}

.game-frame {
    height: calc(100% - 80px);
    width: 100%;
}

.game-frame iframe {
    width: 100%;
    height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .games-grid {
        grid-template-columns: 1fr;
    }
    
    .header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .search-input {
        width: 100%;
    }
}
