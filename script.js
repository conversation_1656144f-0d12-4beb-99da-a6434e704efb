// Game data
const games = [
    {
        id: 1,
        title: "Crazy Dummy Swing Multiplayer",
        description: "Swing through levels with physics-based gameplay in this multiplayer adventure!",
        coverImage: "https://imgs.crazygames.com/games/crazy-dummy-swing-multiplayer/cover_16x9-1747406523876.png?metadata=none&quality=85&width=273&fit=crop",
        embedUrl: "https://www.crazygames.com/embed/crazy-dummy-swing-multiplayer"
    },
    {
        id: 2,
        title: "Coming Soon",
        description: "More exciting games will be added soon!",
        coverImage: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='273' height='154' viewBox='0 0 273 154'%3E%3Crect width='273' height='154' fill='%23333'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='%23fff' font-family='Arial' font-size='16'%3EComing Soon%3C/text%3E%3C/svg%3E",
        embedUrl: null
    },
    {
        id: 3,
        title: "Game Slot 3",
        description: "Another exciting game coming soon!",
        coverImage: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='273' height='154' viewBox='0 0 273 154'%3E%3Crect width='273' height='154' fill='%23444'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='%23fff' font-family='Arial' font-size='16'%3EGame Slot 3%3C/text%3E%3C/svg%3E",
        embedUrl: null
    },
    {
        id: 4,
        title: "Game Slot 4",
        description: "More games to be added here!",
        coverImage: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='273' height='154' viewBox='0 0 273 154'%3E%3Crect width='273' height='154' fill='%23555'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='%23fff' font-family='Arial' font-size='16'%3EGame Slot 4%3C/text%3E%3C/svg%3E",
        embedUrl: null
    }
];

// DOM elements
const gamesGrid = document.getElementById('gamesGrid');
const gameOverlay = document.getElementById('gameOverlay');
const gameIframe = document.getElementById('gameIframe');
const gameTitle = document.getElementById('gameTitle');
const homeBtn = document.getElementById('homeBtn');
const closeBtn = document.getElementById('closeBtn');

// Initialize the app
function init() {
    renderGames();
    setupEventListeners();
}

// Render game cards
function renderGames() {
    gamesGrid.innerHTML = '';
    
    games.forEach(game => {
        const gameCard = createGameCard(game);
        gamesGrid.appendChild(gameCard);
    });
}

// Create a game card element
function createGameCard(game) {
    const card = document.createElement('div');
    card.className = 'game-card';
    card.dataset.gameId = game.id;
    
    card.innerHTML = `
        <img src="${game.coverImage}" alt="${game.title}" class="game-image" loading="lazy">
        <div class="game-info">
            <h3 class="game-title">${game.title}</h3>
            <p class="game-description">${game.description}</p>
        </div>
    `;
    
    // Add click event listener
    card.addEventListener('click', () => {
        if (game.embedUrl) {
            openGame(game);
        } else {
            showComingSoonMessage(game.title);
        }
    });
    
    return card;
}

// Open game in overlay
function openGame(game) {
    gameTitle.textContent = game.title;
    gameIframe.src = game.embedUrl;
    gameOverlay.classList.add('active');
    
    // Add fade-in animation
    gameOverlay.style.opacity = '0';
    setTimeout(() => {
        gameOverlay.style.opacity = '1';
    }, 10);
}

// Close game overlay
function closeGame() {
    gameOverlay.style.opacity = '0';
    setTimeout(() => {
        gameOverlay.classList.remove('active');
        gameIframe.src = '';
        gameTitle.textContent = '';
    }, 300);
}

// Show coming soon message
function showComingSoonMessage(gameTitle) {
    alert(`${gameTitle} is coming soon! Stay tuned for updates.`);
}

// Setup event listeners
function setupEventListeners() {
    // Home button
    homeBtn.addEventListener('click', closeGame);
    
    // Close button
    closeBtn.addEventListener('click', closeGame);
    
    // Escape key to close game
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && gameOverlay.classList.contains('active')) {
            closeGame();
        }
    });
    
    // Click outside game container to close
    gameOverlay.addEventListener('click', (e) => {
        if (e.target === gameOverlay) {
            closeGame();
        }
    });
    

}

// Add smooth transitions
function addTransitions() {
    const style = document.createElement('style');
    style.textContent = `
        .game-overlay {
            transition: opacity 0.3s ease;
        }
        
        .game-card {
            transition: all 0.3s ease, opacity 0.3s ease;
        }
    `;
    document.head.appendChild(style);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    init();
    addTransitions();
});

// Add some visual feedback for loading
window.addEventListener('load', () => {
    document.body.style.opacity = '1';
});

// Initial body opacity for fade-in effect
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.5s ease';
